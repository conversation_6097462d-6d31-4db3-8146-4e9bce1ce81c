<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电源网</title>
  <!-- 第三方css -->
  <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
  <link rel="stylesheet" href="../../modules/layui/css/layui.css">
  <!-- 第三方库js -->
  <script src="../../modules/jq.js"></script>
  <script src="../../modules/layui/layui.js"></script>
  <script src="../../modules/xm-select/xm-select.js"></script>
  <!-- 基础css -->
  <link rel="stylesheet" href="../../css/public.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/footer.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/media.css">
  <link rel="stylesheet" href="../../css/mySidebar.css">
  <!-- 本页私有css -->
  <link rel="stylesheet" href="../../css/pageCss/sample.css">
  <!-- 综合-script -->
  <script src="../../script/index.js" defer></script>
  <!-- 产品分类模块 -->
  <script src="../../script/productCategory.js" defer></script>
  <!-- 所有页面组成部分 -->
  <script src="../../components/common.js" defer></script>
  <script src="../../components/mySidebar.js" defer></script>
  <!-- 单页面组成部分 -->
  <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
  <!-- 引入头部组件 -->
  <div id="header"></div>

  <div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
      <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
      <div>></div>
      <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">应用支持</div>
    </div>
    <div class="dataDown">
      <!-- 侧边栏 -->
      <div class="mySidebar"></div>
      

      <div class="dataDown-right">
        <div class="search">
          <div class="h3">样品申请</div>
        </div>
        <p class="tip">温馨提示:因资源有限，样品借测或送样，仅针对较大的公司或项目，无法保证全部审批通过，敬请理解。您也可以通过线上平台购买样品测试,
          (成品7天/模块15天)内测试不合适可退货、退款。</p>
        <form class="layui-form">
          <div class="layui-form-item">
            <div class="layui-inline" id="formElements">
              <label class="layui-form-label">产品型号</label>
              <div class="layui-input-inline">
                <select name="productModel" lay-verify="required" lay-search>
                  <option value="">请选择</option>
                  <option value="1">layer</option>
                  <option value="2">form</option>
                  <option value="3">layim</option>
                  <option value="4">element</option>
                  <option value="5">laytpl</option>
                  <option value="6">upload</option>
                  <option value="7">laydate</option>
                  <option value="8">laypage</option>
                  <option value="9">flow</option>
                  <option value="10">util</option>
                </select>
              </div>
              <label class="layui-form-label">数量</label>
              <div class="layui-input-inline">
                <input type="number" name="price_min" lay-verify="required" placeholder="" autocomplete="off"
                  class="layui-input" min="1" step="1" lay-affix="number">
              </div>
              <div id="addModel" class="layui-input-inline iconBox">
                <div class="icon">
                  <!-- <i class="layui-icon layui-icon-subtraction"></i> -->
                  <i class="layui-icon layui-icon-addition"></i>
                </div>
                <span @click="addModel">(点击增加型号)</span>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">项目名称</label>
            <div class="layui-input-block">
              <input type="text" name="projectName" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="1">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">预计月/年用量(个)</label>
            <div class="layui-input-block">
              <input type="text" name="dosageNum" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="2">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">项目联系人</label>
            <div class="layui-input-block">
              <input type="text" name="username" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="3">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">公司名称</label>
            <div class="layui-input-block">
              <input type="text" name="company" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="4">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">联系电话</label>
            <div class="layui-input-block">
              <input type="tel" name="phone" lay-verify="required|phone" autocomplete="off" lay-reqtext="请填写手机号"
                lay-affix="clear" value="13800000000" class="layui-input demo-phone">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-inline">
              <input type="text" name="email" lay-verify="required|email" placeholder="有值时才校验" autocomplete="off"
                class="layui-input" value="<EMAIL>">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">所在区域</label>
            <div class="layui-input-inline">
              <select name="quiz1" lay-verify="required" lay-filter="country">
                <option value="中国">中国</option>
                <option value="美国">美国</option>
                <option value="英国">英国</option>
              </select>
            </div>
            <div class="layui-input-inline">
              <select name="quiz2" lay-verify="required">
                <option value="" selected>请选择省</option>
                <option value="浙江">浙江省</option>
                <option value="你的工号">江西省</option>
                <option value="你最喜欢的老师">福建省</option>
              </select>
            </div>
            <div class="layui-input-inline">
              <select name="quiz3" lay-verify="required">
                <option value="">请选择市</option>
                <option value="杭州">杭州</option>
                <option value="宁波" disabled>宁波</option>
                <option value="温州">温州</option>
                <option value="温州">台州</option>
                <option value="温州">绍兴</option>
              </select>
            </div>


          </div>

          <div class="layui-form-item">
            <label class="layui-form-label">详细地址</label>
            <div class="layui-input-block">
              <input type="text" name="address" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="5">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">申请主题</label>
            <div class="layui-input-block">
              <input type="text" name="theme" lay-verify="required" placeholder="请输入" autocomplete="off"
                class="layui-input" value="6">
            </div>
          </div>
          <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">需求描述</label>
            <div class="layui-input-block">
              <textarea id="myTextarea" placeholder="请输入内容" lay-verify="required" class="layui-textarea"></textarea>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">验证码</label>
            <div class="layui-input-inline">
              <div class="layui-row">
                <div class="layui-col-xs7">
                  <div class="layui-input-wrap">
                    <input type="text" name="captcha" value="" lay-verify="required" placeholder="" lay-reqtext="请填写验证码"
                      autocomplete="off" class="layui-input" lay-affix="clear">
                  </div>
                </div>
                <div class="layui-col-xs5">
                  <div style="margin-left: 10px;">
                    <img src="https://www.oschina.net/action/user/captcha"
                      onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-xs4">&nbsp;</div>
          <div class="layui-col-xs4">
            <div class="layui-form-item">
              <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">确认</button>
            </div>
          </div>
        </form>

      </div>


    </div>

  </div>
  </div>

  <div class="bug"></div>

  <!-- 引入底部组件 -->
  <div id="footer"></div>
</body>
<script>
  const data = [
    { text: '应用支持', link: '#' },  // 标题
    { text: '焦点专题', link: '#' },
    { text: '资料下载', link: './downloads.html' },
    { text: '应用视频', link: './videos.html' },
    { text: '常见问题', link: './faq.html' },
    { text: '样品申请', link: './sample.html' },
    { text: '成品检测报告', link: './reports.html' }
  ];
  document.getElementById("myTextarea").value = "新的文本内容";

  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;

      // 添加型号按钮点击事件
      var addModelBtn = document.getElementById('addModel');
      if (addModelBtn) {
        addModelBtn.onclick = function () {
          console.log('添加型号按钮被点击');
          // 获取原始表单元素
          var original = document.getElementById('formElements');
          if (!original) {
            console.error('未找到formElements元素');
            return;
          }

          // 创建新的表单行
          var newRow = document.createElement('div');
          newRow.className = 'layui-inline';
          newRow.innerHTML = `
          <label class="layui-form-label">&nbsp;</label>
          <div class="layui-input-inline">
            <select name="productModel[]" lay-verify="required" lay-search>
              <option value="">请选择</option>
              <option value="1">layer</option>
              <option value="2">form</option>
              <option value="3">layim</option>
              <option value="4">element</option>
              <option value="5">laytpl</option>
              <option value="6">upload</option>
              <option value="7">laydate</option>
              <option value="8">laypage</option>
              <option value="9">flow</option>
              <option value="10">util</option>
            </select>
          </div>
          <label class="layui-form-label">数量</label>
          <div class="layui-input-inline">
            <input type="number" name="price_min[]" lay-verify="required" placeholder="" autocomplete="off" class="layui-input" min="1" step="1" lay-affix="number">
          </div>
          <div class="layui-input-inline iconBox">
                <div class="icon remove-row">
                  <i class="layui-icon layui-icon-subtraction"></i>
                </div>
          </div>
        `;

          // 将新行插入到原始元素后面
          original.parentNode.insertBefore(newRow, original.nextSibling);

          // 重新渲染表单元素
          form.render();

          // 添加删除按钮事件
          var removeBtn = newRow.querySelector('.remove-row');
          if (removeBtn) {
            removeBtn.onclick = function () {
              newRow.remove();
              form.render();
            };
          }
        };
      } else {
        console.error('未找到addModel元素');
      }


      // 监听国家选择变化
      form.on('select(country)', function (data) {
        var countryValue = data.value;
        // console.log('countryValue :>> ', countryValue);
        var provinceSelect = document.querySelector('select[name="quiz2"]').parentNode;
        var citySelect = document.querySelector('select[name="quiz3"]').parentNode;

        // 如果选择的是中国或China，显示省市选择框，否则隐藏
        if (countryValue === '中国' || countryValue === 'China') {
          provinceSelect.style.display = 'inline-block';
          citySelect.style.display = 'inline-block';
        } else {
          provinceSelect.style.display = 'none';
          citySelect.style.display = 'none';
        }
      });


      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值
        // 显示填写结果
        console.log('field :>> ', field);

        // 此处可执行 Ajax 等操作
        // …

        return false; // 阻止默认 form 跳转
      });

      // 日期
      // laydate.render({
      //   elem: '#date'
      // });





    });
  });








</script>


</html>