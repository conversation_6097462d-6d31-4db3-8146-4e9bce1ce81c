﻿@using HlktechPower.Entity
@using Newtonsoft.Json
@{
    var Lid = Language?.Id ?? 0;
    var Code = Language?.UniqueSeoCode ?? "";
    var listProductClass = ProductClass.FindAllByParentIdLan(0, Lid);
    var treeProductClass = ProductClass.FindByTree(0, Lid).ToList();
}
<style asp-location="true">

</style>
<!-- 第三方css -->
<link rel="stylesheet" href="~/modules/iconfont/iconfont.css">
<link rel="stylesheet" href="~/modules/layui/css/layui.css">
<!-- 第三方库js -->
<script src="~/modules/layui/layui.js"></script>
<script src="~/modules/xm-select/xm-select.js"></script>
<!-- 基础css -->
<link rel="stylesheet" href="~/css/public.css">
<link rel="stylesheet" href="~/css/header.css">
<link rel="stylesheet" href="~/css/footer.css">
<link rel="stylesheet" href="~/css/media.css">
<!-- 本页私有css -->
<link rel="stylesheet" href="~/css/pageCss/index.css">
<link rel="stylesheet" href="~/modules/swiper/basic/css/idangerous.swiper.css">
<!-- 本页私有js -->
<script src="~/modules/swiper/basic/js/idangerous.swiper.min.js"></script>
<!-- 综合-script -->
<script src="~/script/index.js"></script>
<body>
    <div class="pageBox">
        <div class="header1">
            <div class="header2">
                <div class="blur noAct" onclick="toRouter(this)" data-link="/">
                    <img src="~/images/power/logo.png" class="headLogo">
                </div>
                <div class="flex productCategory">
                    <label onclick="toRouter(this)" data-select="false"  data-link="@Url.Action("Index","ProductCenter")" class="header2_key"> @T("产品中心")</label> 
                    <div class="productCategoryBox">
                        <!-- 一级页面 -->
                        <div class="productCategory_onePage">
                            @foreach (ProductClass item in listProductClass)
                            {
                                <div data-select="false" onclick="toRouter(this)" data-link="@Url.Action("Aside","ProductCenter",new{Id=item.Id,Name=item.Name})">
                                    <span style="width: 80%;"
                                    class="textOver"> @item.Name </span> <i class="iconfont icon-jiantou2 rightIcon"></i>
                                </div>
                            }
                        </div>
                        <div class="productCategory_hoverPages">
                        </div>
                        <!--  -->
                    </div>
                </div>
                <div class="supportBox" onclick="toRouter(this)" data-select="false" data-link="#">
                    <label class="header2_key">@T("应用支持")</label>
                    <div class="support">
                        <p onclick="toRouter(this)" data-link="@Url.Action("Index", "AppSupport")"> @T("焦点专题")</p>
                        <p onclick="toRouter(this)" data-link="@Url.Action("DataDownload", "AppSupport")"> @T("资料下载")</p>
                        <p onclick="toRouter(this)" data-link="@Url.Action("AppVideo", "AppSupport")"> @T("应用视频")</p>
                        <p onclick="toRouter(this)" data-link="@Url.Action("CommonProblem", "AppSupport")"> @T("常见问题")</p>
                        <p> @T("样品申请")</p>
                        <p> @T("成品检测报告")</p>
                    </div>
                </div>
                <div class="supportBox" onclick="toRouter(this)" data-link="../provider/providerDetail.html">
                    <label class="header2_key">@T("新闻动态")</label>
                    <div class="support">
                        <p> @T("企业动态")</p>
                        <p> @T("产品动态")</p>
                        <p> @T("技术应用")</p>
                    </div>
                </div>
                <div class="supportBox">
                    <label class="header2_key" onclick="toRouter(this)" data-link="../index/bomTool.html">@T("关于我们")</label>
                    <div class="support">
                        <p> @T("企业简介")</p>
                        <p> @T("荣誉资质")</p>
                        <p> @T("企业历程")</p>
                        <p> @T("企业文化")</p>
                    </div>
                </div>
                <div class="supportBox">
                    <label class="header2_key">@T("联系我们")</label>
                    <div class="support">
                        <p> @T("联系信息")</p>
                        <p> @T("建议反馈")</p>
                    </div>
                </div>
                <div>
                    <label class="header2_key">@T("加入我们")</label>
                </div>
                <div>
                    <label class="header2_key" onclick="toRouter(this)" data-link="../index/helper.html">@T("智能选型")</label>
                </div>
                <div class="noAct" style="width: 15%;min-width: 200px;">
                    <div class="searchBox">
                        <!-- <span class="layui-form headerForm">
                        <select class="select" name="select" id="" style="font-size: 1em;" title="分类">
                        <option value="1">商家</option>
                        <option value="2">品牌</option>
                        <option value="3">型号</option>
                        <option value="4" selected>产品规格</option>
                        </select>
                        </span> -->

                        <span class="line"> </span>

                        <input class="searchInput" placeholder="@T("产品资料查询")">
                        <!-- <button class="searchButton flex"> -->
                        <div class="iconfont icon-search my-search"></div>
                        <!-- 搜索 -->
                        <!-- </button> -->


                    </div>
                </div>
                <div class="noAct ml"></div>
                <div class="supportBox">
                    <label class="header2_key cut">@(Code == "cn" ? "ZN" : "EN")</label>
                    <!-- 语言切换-->
                    <div class="support">
                        <p onclick="changelang('cn')"> 简体中文</p>
                        <p onclick="changelang('en')"> English</p>
                    </div>
                </div>
                <div class="supportBox">
                    <label class="header2_key lngText"> <span class="iconfont icon-fenlei"></span></label>
                    <div class="support2">
                        <ul>
                            <li>@T("产品中心")</li>
                            @foreach (ProductClass item in listProductClass)
                            {
                                <li onclick="toRouter(this)" data-link="@Url.Action("Aside", "ProductCenter", new { Id = item.Id, Name = item.Name })">@item.Name</li>
                            }
                        </ul>
                        <ul>
                            <li>@T("应用支持")</li>
                            <li onclick="toRouter(this)" data-link="@Url.Action("Index", "AppSupport")">@T("焦点专题")</li>
                            <li onclick="toRouter(this)" data-link="@Url.Action("DataDownload", "AppSupport")">@T("资料下载")</li>
                            <li onclick="toRouter(this)" data-link="@Url.Action("AppVideo", "AppSupport")">@T("应用视频")</li>
                            <li onclick="toRouter(this)" data-link="@Url.Action("CommonProblem", "AppSupport")">@T("常见问题")</li>
                            <li>@T("样品申请")</li>
                            <li>@T("成品检测报告")</li>
                        </ul>
                        <ul>
                            <li>@T("新闻动态")</li>
                            <li>@T("企业动态")</li>
                            <li>@T("产品动态")</li>
                            <li>@T("技术应用")</li>
                        </ul>
                        <ul>
                            <li>@T("关于我们")</li>
                            <li>@T("企业简介")</li>
                            <li>@T("荣誉资质")</li>
                            <li>@T("企业历程")</li>
                            <li>@T("企业文化")</li>
                        </ul>
                        <ul>
                            <li>@T("联系我们")</li>
                            <li>@T("公司信息")</li>
                            <li>@T("建议反馈")</li>
                        </ul>
                        <ul>
                            <li>@T("加入我们")</li>
                            <li>@T("加入我们")</li>
                        </ul>
                        <ul>
                            <li>@T("功能板块")</li>
                            <li>@T("智能选型")</li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <script>
        // 处理产品分类悬停事件
$(function() {
    // 使用事件委托，确保即使是动态添加的元素也能响应悬停事件
    $(document).on('mouseenter', '.productCategory_onePage > div', function(e) {
        // 获取当前悬停的产品类别文本
        const categoryText = $(this).find('span').text().trim();
        //console.log('悬停分类:', categoryText);
        
        // 根据悬停的类别更新hoverPages内容
        updateHoverPagesContent(categoryText);
        
        // 高亮当前悬停项
        $(this).addClass('active').siblings().removeClass('active');
    });
    
    // 可选：当鼠标离开整个分类区域时重置内容
    $(document).on('mouseleave', '.productCategory_onePage', function() {
        // 可以选择重置为默认内容或保持最后悬停的内容
        // $('.productCategory_hoverPages').html('默认内容');
        // 移除所有高亮
        $('.productCategory_onePage > div').removeClass('active');
    });
    
    //console.log('悬停事件绑定完成，找到元素数量:', $('.productCategory_onePage > div').length);
});
//产品中心内容
const categoryContents = {};
const treelist = @Html.Raw(JsonConvert.SerializeObject(treeProductClass))

for(var i = 0;i<treelist.length;i++){
    if(treelist[i].Child.length <= 0){
        continue;
    }
    var text = '<div>';
    for(var ii = 0;ii<treelist[i].Child.length;ii++){
        text += '<div><h5>'+treelist[i].Child[ii].Name+'</h5>';
        for(var iii = 0;iii<treelist[i].Child[ii].Child.length;iii++){
            var url = '@Url.Action("Series","ProductCenter")?Id='+treelist[i].Child[ii].Child[iii].Id;
            text += '<div class="tag" onclick="toRouter(this)" data-link="'+url+'"><span>'+treelist[i].Child[ii].Child[iii].Name+'</span></div>';
        }
        text += '</div>';
    }
    text += '</div>';
    categoryContents[treelist[i].Name] = text;
}
// 更新悬浮页面内容
function updateHoverPagesContent(category) {
    //console.log('正在更新分类内容:', category);
    // 获取对应类别的内容，如果没有则使用默认内容
    const content = categoryContents[category] || `
        <div>
            <div>
                <h5>${category}</h5>
                <div class="tag"><span>@T("暂无详细信息")</span></div>
            </div>
        </div>
    `;
    
    // 更新悬浮页面内容
    $('.productCategory_hoverPages').html(content);
    console.log('内容已更新');
}

    </script>
</body>

<script asp-location="Footer">
    function changelang(val){
        $.getJSON('/changelang',{id:val},function(res){
            window.location.href = '/'
        })
    }
</script>