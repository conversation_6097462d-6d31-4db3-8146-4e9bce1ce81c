/* 产品详情页面样式 */
.main {
    margin: 0 auto;
    width: 80%;
}

.breadBox {
    background-color:  var(--white);
    display: flex;
    align-items: center;
    padding: 15px 0;
    font-size: 14px;
    border-bottom: 1px solid transparent;
}

.breadBox > div {
    margin-right: 5px;
}

.breadBox > div:first-child,
.textSelect {
    cursor: pointer;
    color: var(--text-color3);
}

.breadBox > div:first-child:hover,
.textSelect:hover {
    color: var(--myBlue);
}

.productDetail {
    margin-top: 20px;
}

.productBox {
    display: flex;
    padding: 30px 0;
    width: 80%;
    margin: 0 auto;
    gap: 20px;
    align-items: stretch; 
    flex-wrap: nowrap; 
}

/* 三个等宽的产品块 */
.product-block {
    flex: 1;
    padding: 20px;
    border-radius: 8px;
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

/* 第一个块：图片块 */
.image-block {
    align-items: center;
    justify-content: center;
    
}

.product-image {
    width: 100%;
    min-width: 300px;
    min-height: 300px;
    border: 1px solid var(--line);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    border-radius: 4px;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
}

.product-image img {
    max-width: 100%;
    height: auto;
}

.image-note {
    font-size: 12px;
    color: var(--myBlue);
    margin-top: 10px;
    text-align: center;
}

/* 第二个块：信息块 */
.info-block {
    justify-content: flex-start;
}

/* 第三个块：操作块 */
.action-block {
    /* display: flex; */
    align-items: center;
    justify-content: center;
    text-align: center;
}

.product-series {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.product-subtitle {
    font-size: 14px;
    color: var(--text-color3);
}

.product-specs {
    margin-top: 20px;
}

.spec-item {
    /* display: flex; */
    margin-bottom: 15px;
    font-size: 15px;
}

.spec-label {
    font-size: 14px;
    color: var(--text-color);
    margin-bottom: 10px;
}
.clo{
  color: var(--myBlue);
}

/* 额外参数样式 */
.additional-specs {
    margin-bottom: 20px;
}

.additional-specs .spec-item {
    margin-bottom: 12px;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
}



.apply-btn {
    background-color: var(--white);
    border: 1px solid var(--line);
    color: var(--text-color1);
}



.product-selection {
    text-align: center;
    margin: 40px 0;
    color: var(--text-color2);
}

.selection-btn {
    margin-top: 15px;
    padding: 10px 30px;
    background-color: var(--myBlue);
    color: var(--white);
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
}