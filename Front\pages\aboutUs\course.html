<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/course.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/mySidebar.js" defer></script>
    <!-- 单页面组成部分 -->
    <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">企业历程</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>

            <div class="introBox">
                <h2>企业历程</h2>
                <div class="company-intro">
                    <div class="layui-timeline">
                        <div class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis layui-icon-face-smile"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="layui-timeline-title">2023年，Layui 情怀版本 2.8.0 发布，新官网上线，且文档全部重写并开源。</div>
                            </div>
                        </div>
                        <div class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis layui-icon-face-cry"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="layui-timeline-title">2021年，Layui 原官网下线，此后 Layui 进入两年的低谷期。</div>
                            </div>
                        </div>
                        <div class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis layui-icon-fire"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="layui-timeline-title">2017年，Layui 里程碑版本 2.0.0 发布，此后 Layui 进入三年的高光期。</div>
                            </div>
                        </div>
                        <div class="layui-timeline-item">
                            <i class="layui-icon layui-timeline-axis layui-icon-circle"></i>
                            <div class="layui-timeline-content layui-text">
                                <div class="layui-timeline-title">2016年，Layui 首个版本发布</div>
                            </div>
                        </div>
   
                    </div>
                </div>

            </div>




        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>
    const data = [
        { text: '关于我们', link: '#' },  // 标题
        { text: '企业简介', link: './intro.html' },
        { text: '荣誉资质', link: './honor.html' },
        { text: '企业历程', link: './course.html' },
        { text: '企业文化', link: './culture.html' },

    ];

</script>

</html>