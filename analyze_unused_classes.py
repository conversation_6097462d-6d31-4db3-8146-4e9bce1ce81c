#!/usr/bin/env python3
import re
import os
import glob

def extract_css_classes_from_file(file_path):
    """从CSS文件中提取所有类名"""
    classes = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 匹配 .className 格式的类名
            matches = re.findall(r'\.([a-zA-Z_-][a-zA-Z0-9_-]*)', content)
            for match in matches:
                # 排除伪类选择器
                if not match.startswith(('hover', 'focus', 'active', 'before', 'after')):
                    classes.add(match)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    return classes

def extract_classes_from_html_files(directory):
    """从HTML文件中提取所有使用的类名"""
    used_classes = set()
    html_files = glob.glob(os.path.join(directory, '**', '*.html'), recursive=True)
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 匹配 class="..." 中的类名
                class_matches = re.findall(r'class=["\']([^"\']*)["\']', content)
                for class_attr in class_matches:
                    # 分割多个类名
                    classes = class_attr.split()
                    used_classes.update(classes)
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
    
    return used_classes

def main():
    # 提取 public.css 中定义的所有类名
    css_file = 'Front/css/public.css'
    defined_classes = extract_css_classes_from_file(css_file)
    
    # 提取 HTML 文件中使用的类名
    html_directory = 'Front/pages'
    used_classes = extract_classes_from_html_files(html_directory)
    
    # 也检查组件文件
    components_directory = 'Front/components'
    component_classes = extract_classes_from_html_files(components_directory)
    used_classes.update(component_classes)
    
    # 找出未使用的类名
    unused_classes = defined_classes - used_classes
    
    print("=== CSS 类名使用情况分析 ===\n")
    print(f"public.css 中定义的类名总数: {len(defined_classes)}")
    print(f"HTML 文件中使用的类名总数: {len(used_classes)}")
    print(f"未使用的类名总数: {len(unused_classes)}")
    
    print("\n=== 未使用的类名列表 ===")
    for class_name in sorted(unused_classes):
        print(f"  .{class_name}")
    
    print("\n=== 已使用的类名列表 ===")
    used_and_defined = used_classes & defined_classes
    for class_name in sorted(used_and_defined):
        print(f"  .{class_name}")

if __name__ == "__main__":
    main()
