/* 引入字体样式 */
@import url('./fonts.css');

/* 其他公共样式 */
* {
    margin: 0;
    padding: 0;
}

body {
    margin: 0;
    padding: 0;
    color: var(--text-color);
}

:root {
    /* 色调 */
    --myBlue: #0E6AF2;
    --blue-deep: #0E6AF2;
    --blue: #0E6AF2;
    --myBlueBg: #0E6AF21A;
    --myBorder: #bbbbbb;
    
    /* 文本 */
    --text-color: #2E2E2E;
    --text-color1: #676767;
    --text-color2: #888888;
    --text-color3: #666666;
    
    /* 边框和分隔线 */
    --line: #d5e1e7d1;
    --line2: #BABABA;
    --line3: #C4C6CF;

    /* 背景 */
    --white: white;
    --bg: #eff1f5;
    --bg2: #445268;
    --bg3: #1B283BCC;
    --bg4: #1664BE;
    --bg5: #f9f9f9;
    --bg6: #D9D9D9;
    --bg7: #0E6AF233;


}

.dataDown {
    display: flex;
    gap: 1vw;
    align-items: flex-start;
    /* 阻止子元素高度拉伸 */
}

.h3 {
    font-size: 1.2vw;
}
.dataDown-right {
    flex: 1;

}
.search {
    display: flex;
    justify-content: space-between;
    margin-right: 50px;
    padding-bottom: 1ch;
    border-bottom: 1px solid var(--line);
    border-radius: 5px;
    margin-bottom: 20px;
    overflow: hidden;
}

.layui-input {
    border-color: var(--myBlue);
}

.layui-input-suffix {
    color: var(--white);
    background: var(--myBlue);
    border: 1px solid var(--myBlue);
    border-radius: 0 0 5px;
}



.layui-laypage .layui-laypage-curr .layui-laypage-em {
    background-color: var(--myBlue);
}

.layui-laypage a:hover {
    color: var(--myBlue);
}

.pageBox {
    width: 100%;
    margin: auto;
    position: relative;
    box-sizing: border-box;

}


.hover:hover {
    color: var(--blue-deep);
    cursor: pointer;
}

.blue {
    color: var(--blue-deep);
}



.flex {
    display: flex;
    justify-content: center;
    place-items: center;
}



a {
    text-decoration: none;
    color: inherit;
    cursor: pointer;
}



button {
    cursor: pointer;
}


.line {
    /* border-left: 1px solid var(--line); */
    height: 23px;
    margin-left: 5px;
}



.textSelect {
    color: var(--myBlue) !important;
}



.textOver {
    /* width: 100%; */
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}



.layui-btn {
    width: 80%;
}

.main {
    margin: 0 auto;
    width: 80%;
    overflow-x: hidden;
    padding-bottom: 50px;
    position: relative;
    /* border: 1px solid #000; */
}

/* 面包屑 --常用公共模块 */
.breadBox {
    padding: .8vw 10px;
    font-size: 15px;
    display: flex;
    border-bottom: none;
}

.breadBox>div {
    color: var(--text-color3);
    cursor: pointer;
    line-height: 30px;
}

.breadBox>div:nth-child(2n) {
    padding: 0px 4px;
}


.bug {
    margin-top: auto;
    height: 20vh;
}

.icon {
    cursor: pointer;
    font-size: 1vw;
}

.icon:hover {
    color: var(--blue-deep);
}

/* 加载 */
@keyframes load {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}