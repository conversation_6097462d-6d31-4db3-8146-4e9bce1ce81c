// 侧边栏组件
const DynamicSidebar = {
  init: function (config) {
    const {
      data,
      containerSelector = ".mySidebar",
      activeItem = null,
    } = config;

    const container = document.querySelector(containerSelector);
    if (!container) {
      console.warn(`找不到侧边栏容器: ${containerSelector}`);
      return false;
    }

    if (!Array.isArray(data) || data.length === 0) {
      console.error("侧边栏数据必须是非空数组");
      return false;
    }

    this.generateSidebar(container, data, activeItem);
    return true;
  },

  generateSidebar: function (container, data, activeItem) {
    // 处理标题
    const titleItem = data[0];
    const titleText =
      typeof titleItem === "object" ? titleItem.text : titleItem;
    const titleLink = typeof titleItem === "object" ? titleItem.link : "#";
    const titleHTML =
      titleLink !== "#"
        ? `<h2 class="title" onclick="toRouter(this)" data-link="${titleLink}">${titleText}</h2>`
        : `<h2 class="title">${titleText}</h2>`;

    // 处理菜单项
    const menuItems = data
      .slice(1)
      .map((item) => {
        const itemText = typeof item === "object" ? item.text : item;
        const itemLink = typeof item === "object" ? item.link : "#";
        const isActive = activeItem ? itemText === activeItem : false;
        const activeClass = isActive ? " textSelect" : "";

        return `<li class="${activeClass.trim()}"
                        onclick="DynamicSidebar.handleItemClick(this)"
                        data-link="${itemLink}"
                        data-menu-item="${itemText}">
                        ${itemText}
                    </li>`;
      })
      .join("");

    const sidebarHTML = `
            <ul>
                ${titleHTML}
                ${menuItems}
            </ul>
        `;

    container.innerHTML = sidebarHTML;
  },

  /**
   * 处理菜单项点击事件
   * @param {Element} element - 被点击的元素
   */
  handleItemClick: function (element) {
    // 保存激活状态到 sessionStorage，以便新页面恢复
    const menuItem = element.getAttribute("data-menu-item");
    if (menuItem) {
      sessionStorage.setItem("activeSidebarItem", menuItem);
    }
    // 更新选中状态（如果不是标题）
    if (element.tagName.toLowerCase() === "li") {
      this.updateActiveState(element);
    }

    // 执行跳转
    toRouter(element);
  },

  /**
   * 更新激活状态
   * @param {Element} activeElement - 当前激活的元素
   */
  updateActiveState: function (activeElement) {
    // 移除同级所有元素的激活状态
    const allItems = activeElement.parentElement.querySelectorAll("li");
    allItems.forEach((item) => item.classList.remove("textSelect"));

    // 添加当前元素的激活状态
    activeElement.classList.add("textSelect");
  },

  /**
   * 设置激活项
   * @param {string} menuItem - 要激活的菜单项名称
   * @param {string} containerSelector - 容器选择器
   */
  setActiveItem: function (menuItem, containerSelector = ".mySidebar") {
    const container = document.querySelector(containerSelector);
    if (!container) return;

    const targetItem = container.querySelector(
      `[data-menu-item="${menuItem}"]`
    );
    if (targetItem && targetItem.tagName.toLowerCase() === "li") {
      this.updateActiveState(targetItem);
    }
  },
};

// 页面加载时初始化
document.addEventListener("DOMContentLoaded", () => {
  // 使用页面中的data数组
  if (typeof data !== "undefined" && Array.isArray(data)) {
    const success = DynamicSidebar.init({
      data: data,
      containerSelector: ".mySidebar",
    });

    if (success) {
      // 从 sessionStorage 中恢复激活状态
      setTimeout(() => {
        const savedActiveItem = sessionStorage.getItem("activeSidebarItem");
        if (savedActiveItem) {
          DynamicSidebar.setActiveItem(savedActiveItem);
          // 清除存储的状态
          // sessionStorage.removeItem("activeSidebarItem");
        }
      }, 100);
    }
  } else {
    console.error("未找到data数组或data不是数组类型");
  }
});
