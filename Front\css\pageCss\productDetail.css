/* 某个供应商详情页面的css样式 */
.main {
    margin: 0 auto;
    width: calc(80% -40px);
}

.breadBox {
    background-color: var(--white);
    border-bottom: 1px solid var(--line);
}

.contentBox {
    display: flex;
    align-items: flex-start;
    margin-top: 20px;
}

/* 侧边栏样式已移至 mySidebar.css 统一管理 */

.content {
    padding-left: 2%;
    width: 80%;
}

/* .logoBox{
    margin-top: 1vw;
    padding: 2vw 0px;
} */
.productBox {
    padding: 1.2vw 0px;
    border-bottom: 1px solid var(--line);
    display: flex;
    flex-direction: column;
}

.productBox2 {}

.productTitle {
    font-size: 17px;
    color: black;
    font-weight: 500;
    cursor: pointer;
}

.productTitle2 {
    border-bottom: 1px solid var(--line);
    padding: 1.2vw 0px;
}

.productList {
    margin-left: 2%;
    display: flex;
    /* justify-content: space-around; */
    flex-wrap: wrap;
}

.introduceItem {
    width: 31%;
    margin-left: 1%;
    padding: 5px 15px;
    height: 70px;
    display: flex;
    flex-wrap: wrap;
    place-items: center;
    position: relative;
    box-sizing: border-box;
    color: var(--blue);
    cursor: pointer;
    font-size: 15px;
}

.introduceItem::before {
    content: '';
    position: absolute;
    top: calc(50% - 3px);
    left: 0px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--blue);
}

.count {
    color: var(--text-color3);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.product-card {
    border: 1px solid var(--line);
    border-top: 1px solid var(--myBlue);
    border-radius: 5px;
    background-color: var(--bg5);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.product-card img {
    max-width: 100%;
    height: 120px;
    object-fit: contain;
    display: block;
    margin: 0 10px;
}

.product-info h3 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 500;
}

.product-info p {
    color: var(--text-color3);
    margin: 5px 0;
    font-size: 14px;
}

.product-selection,
#demo-laypage-all {
    text-align: right;
    margin-right: 20px;
    color: var(--myBlue);
    cursor: pointer;
    font-size: 15px;
}

.tableBox {
    border: 1px solid var(--line2);
    border-radius: 10px;
    /* table圆角*/
    overflow: hidden;
    margin: 10px 0 20px;
}

.layui-table {
    border-radius: 10px;
    overflow: hidden;
    margin: 0;
}

.layui-table thead {
    background-color: var(--bg6) !important;
    text-align: center;
}

.long {
    width: 90px !important;
}

.layui-table th {
    font-weight: 400;
    color: var(--text-color);
}

.layui-table th,
.layui-table td {
    padding: 5px !important;
    width: 50px;
    text-align: center;
    font-size: 14px;
    line-height: normal;
    /* border-color: #BABABA; */
}



.blue {
    color: var(--myBlue);
}

.renzhengBox {
    width: 80px !important;
    overflow: hidden;
}

.renzhengBox img {
    position: absolute;
    top: 50%;
    left: 106px;
    transform: scale(5);
    filter: grayscale(100%) brightness(0);
}

.series-grid {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding-bottom: 30px;
}

.grid-left {
    width: 70%;
}

.grid-left p {
    margin-bottom: 10px;
    color:  var(--text-color3);
}

.grid-left p:first-child {
    font-size: 15px;
    color: var(--myBlue);
    margin: 20px 0;
}

.grid-right {
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.grid-right img {
    width: 55%;
    height: 55%;
    object-fit: contain;
}