/* 字体定义 */
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Regular.otf") format("opentype");
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Bold.otf") format("opentype");
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Normal_Normal.otf") format("opentype");
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Light_Light.otf") format("opentype");
  font-style: normal;
  font-display: swap;
}

/* 图标字体通用样式 */
/* .iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

/* 正文字体设置 */
body {
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
  font-size: 14px;
}

/* 标题字体 */
h1,
h2,
h3,
h4,
h5,
h6 {
  /* Hiragino Sans GB",  */
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
}

/* 字体大小变量 */
:root {
  --fs-xs: 12px;
  --fs-sm: 14px;
  --fs-md: 16px;
  --fs-lg: 18px;
  --fs-xl: 24px;
}

/* 辅助类 */
.text-xs {
  font-size: var(--fs-xs);
}

.text-sm {
  font-size: var(--fs-sm);
}

.text-md {
  font-size: var(--fs-md);
}

.text-lg {
  font-size: var(--fs-lg);
}

.text-xl {
  font-size: var(--fs-xl);
}