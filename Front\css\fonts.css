/* 字体定义 */
@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Normal.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Source Han Sans CN";
  src: url("fonts/Source_Han_Sans_SC_Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

/* 图标字体通用样式 */
/* .iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

/* 正文字体设置 */
body {
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
  font-size: 14px;
}

/* 标题字体 */
h1,
h2,
h3,
h4,
h5,
h6 {
  /* Hiragino Sans GB",  */
  font-family: "Source Han Sans CN", "Microsoft Yahei", Arial, sans-serif;
  font-weight: 500;
}

/* 字体大小变量 */
:root {
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
}

/* 辅助类 */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-md {
  font-size: var(--font-size-md);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}