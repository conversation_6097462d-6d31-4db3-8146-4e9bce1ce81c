<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电源网</title>
    <!-- 第三方css -->
    <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
    <link rel="stylesheet" href="../../modules/layui/css/layui.css">
    <!-- 第三方库js -->
    <script src="../../modules/jq.js"></script>
    <script src="../../modules/layui/layui.js"></script>
    <script src="../../modules/xm-select/xm-select.js"></script>
    <!-- 基础css -->
    <link rel="stylesheet" href="../../css/public.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/footer.css">
    <link rel="stylesheet" href="../../css/header.css">
    <link rel="stylesheet" href="../../css/media.css">
    <link rel="stylesheet" href="../../css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="../../css/pageCss/companyIng.css">
    <!-- 综合-script -->
    <script src="../../script/index.js" defer></script>
    <!-- 产品分类模块 -->
    <script src="../../script/productCategory.js" defer></script>
    <!-- 所有页面组成部分 -->
    <script src="../../components/common.js" defer></script>
    <script src="../../components/mySidebar.js" defer></script>
    <!-- 单页面组成部分 -->
    <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">企业动态</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>


            <div class="dataDown-right">
                <div class="search">
                    <div class="h3">企业动态</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input">
                        <div class="layui-input-split layui-input-suffix">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen">搜索</span>
                        </div>
                    </div>
                </div>
                <div class="companyIng">
                    <div class="company-item">
                        <div class="item-image">
                            <img src="../../images/power/qiyedongtai.png" alt="News Image">
                        </div>
                        <div class="item-content">
                            <div class="item-title">无需接线!1个底板可测试海凌科5款人脸识别模块</div>
                            <div class="item-description">海凌科在售5款人脸识别模块都可以使用FO101模组这一款测试底板进行测试</div>
                            <div class="item-date">发布时间: 2025-05-12 12:48</div>
                            <a href="#" class="item-button">查看详情</a>
                        </div>
                    </div>
                    <div class="company-item">
                        <div class="item-image">
                            <img src="../../images/power/qiyedongtai.png" alt="News Image">
                        </div>
                        <div class="item-content">
                            <div class="item-title">无需接线!1个底板可测试海凌科5款人脸识别模块</div>
                            <div class="item-description">海凌科在售5款人脸识别模块都可以使用FO101模组这一款测试底板进行测试</div>
                            <div class="item-date">发布时间: 2025-05-12 12:48</div>
                            <a href="#" class="item-button">查看详情</a>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div id="demo-laypage-all"></div>
                </div>
            </div>



        </div>
    </div>

    <div class="bug"></div>

    <!-- 引入底部组件 -->
    <div id="footer"></div>
</body>
<script>
    const data = [
        { text: '新闻动态', link: '#' },  // 标题
        { text: '企业动态', link: './companyIng.html' },
        { text: '产品动态', link: './productIng.html' },
        { text: '技术应用', link: './tech.html' },

    ];
    // 初始化分页器
    layui.use(['laypage'], function () {
        var laypage = layui.laypage;
        // 完整显示
        laypage.render({
            elem: 'demo-laypage-all', // 元素 id
            count: 100, // 数据总数
            layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip'], // 功能布局
            jump: function (obj) {
                // console.log(obj);
            }
        });
    });

</script>

</html>